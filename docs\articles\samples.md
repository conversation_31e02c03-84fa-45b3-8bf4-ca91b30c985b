# Samples

Explore real-world examples and code snippets to help you get started and master DrawnUi.Maui.

## Example Apps

### Learning Projects

- **[Sandbox Project](https://github.com/taublast/DrawnUi.Maui/tree/main/src/Maui/Samples/Sandbox)** 🧪 - Experiment with:
  - Pre-built drawn controls
  - Playground examples
  - Custom controls development
  - Maps integration
  - Various styling approaches

- **[Engine Demo](https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo)** 🤩 - A comprehensive totally drawn app demo featuring:
  - Recycled cells and virtualization
  - Camera integration examples
  - Custom controls showcase
  - Updated with latest NuGet package

### Complete Demo Applications

- **[Space Shooter Game](https://github.com/taublast/AppoMobi.Maui.DrawnUi.SpaceShooter)** 🎮 - Dynamic arcade game demonstrating:
  - Full keyboard support implementation
  - Game-specific UI patterns
  - High-performance rendering
  - Uses preview NuGet with SkiaSharp v3

- **[Shaders Carousel](https://github.com/taublast/ShadersCarousel/)** ✨ - Advanced SkiaSharp v3 capabilities:
  - Custom shader effects
  - Visual effects showcase
  - Hardware acceleration examples
  - Modern rendering techniques


## Code Snippets
- [Game UI & Interactive Games](advanced/game-ui.md)
- [Gradients](advanced/gradients.md)
- [SkiaScroll & Virtualization](advanced/skiascroll.md)
- [Gestures & Touch Input](advanced/gestures.md)

## Production Apps

Real-world applications built with DrawnUI:

### Bug ID: Insect Identifier AI
_Totally drawn with just one root view `Canvas` and `SkiaShell` for navigation. First ever totally drawn published MAUI app._

- **Video Demo**: [Watch on YouTube](https://www.youtube.com/watch?v=5QIaM0xsLbA)
- **Architecture**: Single Canvas root with SkiaShell navigation
- **Features**: AI-powered insect identification with fully drawn UI

### Racebox - Vehicle Dynamics
_MAUI native pages with canvases and custom navigation. All scrolls, cells collections, maps, buttons, labels and custom controls are drawn._

- **Video Demo**: [Watch on YouTube](https://www.youtube.com/watch?v=JQkJhXR9IMY)
- **Architecture**: MAUI pages with Canvas components
- **Features**: Vehicle telemetry, maps, data visualization, custom controls

## Contributing Samples
Have a cool UI or feature? Submit a PR or open an issue to share your sample with the community!