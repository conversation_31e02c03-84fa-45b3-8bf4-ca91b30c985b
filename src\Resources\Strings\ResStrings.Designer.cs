﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Breakout.Resources.Strings {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResStrings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResStrings() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Breakout.Resources.Strings.ResStrings", typeof(ResStrings).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About.
        /// </summary>
        public static string AboutApp {
            get {
                return ResourceManager.GetString("AboutApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        public static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All.
        /// </summary>
        public static string All {
            get {
                return ResourceManager.GetString("All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount.
        /// </summary>
        public static string Amount {
            get {
                return ResourceManager.GetString("Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are You sure?.
        /// </summary>
        public static string AreYouSure {
            get {
                return ResourceManager.GetString("AreYouSure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgot password?.
        /// </summary>
        public static string AuthForgotPasswordQuestion {
            get {
                return ResourceManager.GetString("AuthForgotPasswordQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        public static string AuthLogin {
            get {
                return ResourceManager.GetString("AuthLogin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remember me.
        /// </summary>
        public static string AuthRememberMe {
            get {
                return ResourceManager.GetString("AuthRememberMe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Balance.
        /// </summary>
        public static string Balance {
            get {
                return ResourceManager.GetString("Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birthday.
        /// </summary>
        public static string Birthday {
            get {
                return ResourceManager.GetString("Birthday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply.
        /// </summary>
        public static string BtnApply {
            get {
                return ResourceManager.GetString("BtnApply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string BtnCancel {
            get {
                return ResourceManager.GetString("BtnCancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string BtnClose {
            get {
                return ResourceManager.GetString("BtnClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connect.
        /// </summary>
        public static string BtnConnect {
            get {
                return ResourceManager.GetString("BtnConnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue.
        /// </summary>
        public static string BtnContinue {
            get {
                return ResourceManager.GetString("BtnContinue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create.
        /// </summary>
        public static string BtnCreate {
            get {
                return ResourceManager.GetString("BtnCreate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disconnect.
        /// </summary>
        public static string BtnDisconnect {
            get {
                return ResourceManager.GetString("BtnDisconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Go Back.
        /// </summary>
        public static string BtnGoBack {
            get {
                return ResourceManager.GetString("BtnGoBack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        public static string BtnOk {
            get {
                return ResourceManager.GetString("BtnOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Play Again.
        /// </summary>
        public static string BtnPlayAgain {
            get {
                return ResourceManager.GetString("BtnPlayAgain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask A Question.
        /// </summary>
        public static string BtnQuestion {
            get {
                return ResourceManager.GetString("BtnQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quit.
        /// </summary>
        public static string BtnQuit {
            get {
                return ResourceManager.GetString("BtnQuit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset.
        /// </summary>
        public static string BtnReset {
            get {
                return ResourceManager.GetString("BtnReset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string BtnSave {
            get {
                return ResourceManager.GetString("BtnSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Share.
        /// </summary>
        public static string BtnShare {
            get {
                return ResourceManager.GetString("BtnShare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        public static string BtnStart {
            get {
                return ResourceManager.GetString("BtnStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stop.
        /// </summary>
        public static string BtnStop {
            get {
                return ResourceManager.GetString("BtnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Camera.
        /// </summary>
        public static string Camera {
            get {
                return ResourceManager.GetString("Camera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cameras.
        /// </summary>
        public static string Cameras {
            get {
                return ResourceManager.GetString("Cameras", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Balance.
        /// </summary>
        public static string ChangeBalance {
            get {
                return ResourceManager.GetString("ChangeBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please confirm this action!.
        /// </summary>
        public static string ConfirmAction {
            get {
                return ResourceManager.GetString("ConfirmAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmation Needed.
        /// </summary>
        public static string ConfirmationNeeded {
            get {
                return ResourceManager.GetString("ConfirmationNeeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this entry?.
        /// </summary>
        public static string ConfirmDelete {
            get {
                return ResourceManager.GetString("ConfirmDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dashboard.
        /// </summary>
        public static string Dashboard {
            get {
                return ResourceManager.GetString("Dashboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        public static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days.
        /// </summary>
        public static string Days {
            get {
                return ResourceManager.GetString("Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string Deleting {
            get {
                return ResourceManager.GetString("Deleting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Demo.
        /// </summary>
        public static string DemoMode {
            get {
                return ResourceManager.GetString("DemoMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Difficulty.
        /// </summary>
        public static string Difficulty {
            get {
                return ResourceManager.GetString("Difficulty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        public static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edited.
        /// </summary>
        public static string Edited {
            get {
                return ResourceManager.GetString("Edited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit List.
        /// </summary>
        public static string EditList {
            get {
                return ResourceManager.GetString("EditList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to E-mail.
        /// </summary>
        public static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End.
        /// </summary>
        public static string End {
            get {
                return ResourceManager.GetString("End", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Europe.
        /// </summary>
        public static string Europe {
            get {
                return ResourceManager.GetString("Europe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event.
        /// </summary>
        public static string Event {
            get {
                return ResourceManager.GetString("Event", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Events.
        /// </summary>
        public static string Events {
            get {
                return ResourceManager.GetString("Events", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter.
        /// </summary>
        public static string Filter {
            get {
                return ResourceManager.GetString("Filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filtered.
        /// </summary>
        public static string Filtered {
            get {
                return ResourceManager.GetString("Filtered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name.
        /// </summary>
        public static string FirstName {
            get {
                return ResourceManager.GetString("FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full Name.
        /// </summary>
        public static string FullName {
            get {
                return ResourceManager.GetString("FullName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group.
        /// </summary>
        public static string Group {
            get {
                return ResourceManager.GetString("Group", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group.
        /// </summary>
        public static string Groups {
            get {
                return ResourceManager.GetString("Groups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to History.
        /// </summary>
        public static string History {
            get {
                return ResourceManager.GetString("History", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home.
        /// </summary>
        public static string Home {
            get {
                return ResourceManager.GetString("Home", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  h.
        /// </summary>
        public static string HoursShort {
            get {
                return ResourceManager.GetString("HoursShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Info.
        /// </summary>
        public static string Info {
            get {
                return ResourceManager.GetString("Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Interaction.
        /// </summary>
        public static string Interaction {
            get {
                return ResourceManager.GetString("Interaction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language.
        /// </summary>
        public static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name.
        /// </summary>
        public static string LastName {
            get {
                return ResourceManager.GetString("LastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Leads.
        /// </summary>
        public static string Leads {
            get {
                return ResourceManager.GetString("Leads", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lvl.
        /// </summary>
        public static string Lev {
            get {
                return ResourceManager.GetString("Lev", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Level.
        /// </summary>
        public static string Level {
            get {
                return ResourceManager.GetString("Level", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lives.
        /// </summary>
        public static string Lives {
            get {
                return ResourceManager.GetString("Lives", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log Out.
        /// </summary>
        public static string LogOut {
            get {
                return ResourceManager.GetString("LogOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manager.
        /// </summary>
        public static string Manager {
            get {
                return ResourceManager.GetString("Manager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Managers.
        /// </summary>
        public static string Managers {
            get {
                return ResourceManager.GetString("Managers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 🎉 Amazing! You&apos;ve conquered all levels! 🎉
        ///Final Score: {0}
        ///You&apos;re a Breakout Master!.
        /// </summary>
        public static string MessageGameComplete {
            get {
                return ResourceManager.GetString("MessageGameComplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 😔 Game Over! 💔
        ///Final Score: {0}
        ///Ready for another round?.
        /// </summary>
        public static string MessageGameOver {
            get {
                return ResourceManager.GetString("MessageGameOver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 🎯 Level {0} Complete! 🎉
        ///Score: {1}
        ///Get ready for Level {2}!.
        /// </summary>
        public static string MessageLevelComplete {
            get {
                return ResourceManager.GetString("MessageLevelComplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 💥 Welcome to Breakout! 🏓
        ///🏆 Smash all bricks, victory awaits!.
        /// </summary>
        public static string MessageWelcome {
            get {
                return ResourceManager.GetString("MessageWelcome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 💥 Welcome to Breakout! 🏓
        ///Use mouse/keyboard to control. 
        ///🏆 Smash all bricks, victory awaits!.
        /// </summary>
        public static string MessageWelcomeDesktop {
            get {
                return ResourceManager.GetString("MessageWelcomeDesktop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Messenger.
        /// </summary>
        public static string Messenger {
            get {
                return ResourceManager.GetString("Messenger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle Name.
        /// </summary>
        public static string MiddleName {
            get {
                return ResourceManager.GetString("MiddleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Profile.
        /// </summary>
        public static string MyProfile {
            get {
                return ResourceManager.GetString("MyProfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None.
        /// </summary>
        public static string None {
            get {
                return ResourceManager.GetString("None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unallowed.
        /// </summary>
        public static string NoRights {
            get {
                return ResourceManager.GetString("NoRights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Off.
        /// </summary>
        public static string Off {
            get {
                return ResourceManager.GetString("Off", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Office.
        /// </summary>
        public static string Office {
            get {
                return ResourceManager.GetString("Office", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offices.
        /// </summary>
        public static string Offices {
            get {
                return ResourceManager.GetString("Offices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On.
        /// </summary>
        public static string On {
            get {
                return ResourceManager.GetString("On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Options.
        /// </summary>
        public static string Options {
            get {
                return ResourceManager.GetString("Options", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order.
        /// </summary>
        public static string Order {
            get {
                return ResourceManager.GetString("Order", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order.
        /// </summary>
        public static string OrderBy {
            get {
                return ResourceManager.GetString("OrderBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Newer first.
        /// </summary>
        public static string OrderNewFirst {
            get {
                return ResourceManager.GetString("OrderNewFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Older first.
        /// </summary>
        public static string OrderOldFirst {
            get {
                return ResourceManager.GetString("OrderOldFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orders.
        /// </summary>
        public static string Orders {
            get {
                return ResourceManager.GetString("Orders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivered to client.
        /// </summary>
        public static string OrderStatus_Issued {
            get {
                return ResourceManager.GetString("OrderStatus_Issued", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Processing.
        /// </summary>
        public static string OrderStatus_Received {
            get {
                return ResourceManager.GetString("OrderStatus_Received", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sent to office.
        /// </summary>
        public static string OrderStatus_Sent {
            get {
                return ResourceManager.GetString("OrderStatus_Sent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        public static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Past Due.
        /// </summary>
        public static string PastDue {
            get {
                return ResourceManager.GetString("PastDue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone.
        /// </summary>
        public static string PhoneNumber {
            get {
                return ResourceManager.GetString("PhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Press Back once again to exit...
        /// </summary>
        public static string PressBACKOnceAgain {
            get {
                return ResourceManager.GetString("PressBACKOnceAgain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price.
        /// </summary>
        public static string Price {
            get {
                return ResourceManager.GetString("Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product.
        /// </summary>
        public static string Product {
            get {
                return ResourceManager.GetString("Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Products.
        /// </summary>
        public static string Products {
            get {
                return ResourceManager.GetString("Products", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Push.
        /// </summary>
        public static string Push {
            get {
                return ResourceManager.GetString("Push", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pushes.
        /// </summary>
        public static string Pushes {
            get {
                return ResourceManager.GetString("Pushes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity.
        /// </summary>
        public static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region.
        /// </summary>
        public static string Region {
            get {
                return ResourceManager.GetString("Region", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regions.
        /// </summary>
        public static string Regions {
            get {
                return ResourceManager.GetString("Regions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove.
        /// </summary>
        public static string Remove {
            get {
                return ResourceManager.GetString("Remove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request.
        /// </summary>
        public static string Request {
            get {
                return ResourceManager.GetString("Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requests.
        /// </summary>
        public static string Requests {
            get {
                return ResourceManager.GetString("Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Password.
        /// </summary>
        public static string ResetPassword {
            get {
                return ResourceManager.GetString("ResetPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rows per page.
        /// </summary>
        public static string RowsPerPage {
            get {
                return ResourceManager.GetString("RowsPerPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Score.
        /// </summary>
        public static string Score {
            get {
                return ResourceManager.GetString("Score", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        public static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select.
        /// </summary>
        public static string Select {
            get {
                return ResourceManager.GetString("Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shop.
        /// </summary>
        public static string Shop {
            get {
                return ResourceManager.GetString("Shop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        public static string Start {
            get {
                return ResourceManager.GetString("Start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Game.
        /// </summary>
        public static string StartGame {
            get {
                return ResourceManager.GetString("StartGame", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paused.
        /// </summary>
        public static string StatePaused {
            get {
                return ResourceManager.GetString("StatePaused", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other.
        /// </summary>
        public static string StudyPlanType_Other {
            get {
                return ResourceManager.GetString("StudyPlanType_Other", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Success!.
        /// </summary>
        public static string Success {
            get {
                return ResourceManager.GetString("Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System Settings.
        /// </summary>
        public static string SystemSettings {
            get {
                return ResourceManager.GetString("SystemSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teacher.
        /// </summary>
        public static string Teacher {
            get {
                return ResourceManager.GetString("Teacher", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teachers.
        /// </summary>
        public static string Teachers {
            get {
                return ResourceManager.GetString("Teachers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text.
        /// </summary>
        public static string Text {
            get {
                return ResourceManager.GetString("Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time.
        /// </summary>
        public static string Time {
            get {
                return ResourceManager.GetString("Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Admin Panel.
        /// </summary>
        public static string TitleDesk {
            get {
                return ResourceManager.GetString("TitleDesk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translation.
        /// </summary>
        public static string Translation {
            get {
                return ResourceManager.GetString("Translation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        public static string Type {
            get {
                return ResourceManager.GetString("Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unset.
        /// </summary>
        public static string Unset {
            get {
                return ResourceManager.GetString("Unset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User.
        /// </summary>
        public static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username.
        /// </summary>
        public static string UserName {
            get {
                return ResourceManager.GetString("UserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users.
        /// </summary>
        public static string Users {
            get {
                return ResourceManager.GetString("Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} field not filled.
        /// </summary>
        public static string ValidationFieldRequired {
            get {
                return ResourceManager.GetString("ValidationFieldRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required field.
        /// </summary>
        public static string ValidationRequired {
            get {
                return ResourceManager.GetString("ValidationRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Demo.
        /// </summary>
        public static string VendorTitle {
            get {
                return ResourceManager.GetString("VendorTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version.
        /// </summary>
        public static string Version {
            get {
                return ResourceManager.GetString("Version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome.
        /// </summary>
        public static string Welcome {
            get {
                return ResourceManager.GetString("Welcome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Word.
        /// </summary>
        public static string Word {
            get {
                return ResourceManager.GetString("Word", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Words.
        /// </summary>
        public static string Words {
            get {
                return ResourceManager.GetString("Words", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wrong username or password.
        /// </summary>
        public static string WrongPassword {
            get {
                return ResourceManager.GetString("WrongPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
