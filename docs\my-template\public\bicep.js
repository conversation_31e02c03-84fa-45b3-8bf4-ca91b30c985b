// https://github.com/Azure/bicep/blob/main/src/highlightjs/dist/bicep.es.min.js
"use strict";const o=n=>`\\b${n}\\b`,l=n=>`(?<=${n})`,u=n=>`(?<!${n})`,i=n=>`(?=${n})`,c=n=>`(?!${n})`,g="[_a-zA-Z]",b="[_a-zA-Z0-9]",s=o(`${g}${b}*`),r=o("[_a-zA-Z-0-9]+"),e="(?:[ \\t\\r\\n]|\\/\\*(?:\\*(?!\\/)|[^*])*\\*\\/)*",d={$pattern:"[A-Za-z$_][0-9A-Za-z$_]*",keyword:["metadata","targetScope","resource","module","param","var","output","for","in","if","existing","import","as","type","with","using","extends","func","assert","extension"],literal:["true","false","null"],built_in:["az","sys"]},f={className:"comment",match:`//.*${i("$")}`},M={className:"comment",begin:"/\\*",end:"\\*/"},p={variants:[f,M]};function t(n){return[...n,p]}const a={variants:[]},N={match:"\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\|'|\\${)"},h={className:"string",begin:"'''",end:`'''${c("'")}`},v={className:"subst",begin:`${u("\\\\")}(\\\${)`,end:"(})",contains:t([a])},A={className:"string",begin:`'${c("''")}`,end:"'",contains:[N,v]},L={className:"number",match:"[0-9]+"},S={className:"literal",match:o("(true|false|null)"),relevance:0},m={className:"variable",match:`${s}${c(`${e}\\(`)}`,keywords:d},y={begin:"{",end:"}",contains:t([{className:"property",match:`${s}${i(`${e}:`)}`,relevance:0},a])},_={begin:`\\[${c(`${e}${o("for")}`)}`,end:"]",contains:t([a])},$={className:"function",begin:`(${s})${e}\\(`,end:"\\)",contains:t([a])},w={className:"meta",begin:`@${e}${i(s)}`,end:"",contains:t([$])},x=`(\\(${e}${s}${e}(,${e}${s}${e})*\\)|\\(${e}\\)|${e}${s}${e})${i(`${e}=>`)}`,z={begin:x,returnBegin:!0,end:`${e}=>`,contains:t([m])},C={begin:`${l(`^${e}`)}#${r}`,end:"$",className:"meta",contains:t([{className:"variable",match:r}])};a.variants=[A,h,L,S,y,_,m,$,w,z,C];export default function(n){return{aliases:["bicep"],case_insensitive:!0,keywords:d,contains:t([a])}}