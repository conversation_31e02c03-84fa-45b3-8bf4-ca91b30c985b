/* ===== LIGHT MODE (DEFAULT) ===== */

/* Light mode code styling - uses default DocFX colors */
/* (Empty - let DocFX handle light mode styling) */

/* ===== DARK MODE SYNTAX HIGHLIGHTING ===== */
@media (prefers-color-scheme: dark) {
    /* Code block background */
    pre, code {
        background-color: #000000 !important;
        color: #dfdfdf !important;
    }

    /* Comments */
    .hljs-comment,
    .hljs-quote {
        color: #4fe34f !important;
    }

    /* XML Documentation Comments */
    .hljs-doctag,
    .hljs-meta .hljs-doctag {
        color: #0ab90A !important;
    }

    /* Strings */
    .hljs-string,
    .hljs-literal {
        color: #edf5a0 !important;
    }

    /* String interpolation/templates */
    .hljs-template-string,
    .hljs-template-variable {
        color: #fb7773 !important;
    }

    /* Keywords (if, else, for, while, etc.) */
    .hljs-keyword {
        color: #e2dc29 !important;
    }

    /* Control flow keywords */
    .hljs-keyword.hljs-control,
    .hljs-keyword.hljs-flow {
        color: #ea97e5 !important;
    }

    /* Types, classes, interfaces */
    .hljs-type,
    .hljs-class,
    .hljs-title.class_,
    .hljs-built_in {
        color: #edf5a0 !important;
    }

    /* Enums */
    .hljs-enum {
        color: #74c88f !important;
    }

    /* Method/Function names */
    .hljs-title.function_,
    .hljs-function .hljs-title,
    .hljs-method {
        color: #f9ffb4 !important;
    }

    /* Variables, properties */
    .hljs-variable,
    .hljs-property,
    .hljs-attr {
        color: #9CDCFE !important;
    }

    /* Constants, enum members */
    .hljs-variable.constant_,
    .hljs-constant {
        color: #dfdfdf !important;
    }

    /* Numbers */
    .hljs-number {
        color: #dfdfdf !important;
    }

    /* Operators */
    .hljs-operator {
        color: #e2dc29 !important;
    }

    /* Punctuation */
    .hljs-punctuation {
        color: #cfcfcf !important;
    }

    /* Preprocessor directives */
    .hljs-meta,
    .hljs-preprocessor {
        color: #b8b8b8 !important;
    }

    /* Attributes */
    .hljs-attribute,
    .hljs-meta-string {
        color: #dfdfdf !important;
    }

    /* Namespaces */
    .hljs-namespace {
        color: #dfdfdf !important;
    }

    /* C# specific */
    .hljs-keyword.hljs-type {
        color: #e2dc29 !important;
    }

    .hljs-keyword.hljs-modifier {
        color: #e2dc29 !important;
    }

    /* Generic type parameters */
    .hljs-title.class_.generic_ {
        color: #edf5a0 !important;
    }

    /* LINQ keywords */
    .hljs-keyword.hljs-linq {
        color: #ea97e5 !important;
    }

    /* Override default code block styling */
    .hljs {
        background: #000000 !important;
        color: #dfdfdf !important;
    }

    /* Ensure proper contrast for inline code */
    code {
        background-color: #111111 !important;
        color: #dfdfdf !important;
        padding: 2px 4px !important;
        border-radius: 3px !important;
    }

    /* Line numbers (if enabled) */
    .hljs-ln-numbers {
        color: #666666 !important;
        border-right: 1px solid #333333 !important;
        padding-right: 8px !important;
    }
} /* End of dark mode media query */

/* ===== CUSTOM SPACING AND TYPOGRAPHY ===== */

/* Main content area spacing */
.article {
    line-height: 1.6;
}

/* Heading spacing improvements */
h1 {
    margin-top: 2rem !important;
    margin-bottom: 1.5rem !important;
}

h2 {
    margin-top: 2.5rem !important;
    margin-bottom: 1.25rem !important;
}

h3 {
    margin-top: 2rem !important;
    margin-bottom: 1rem !important;
}

h4 {
    margin-top: 1.5rem !important;
    margin-bottom: 0.75rem !important;
}

h5, h6 {
    margin-top: 1.25rem !important;
    margin-bottom: 0.5rem !important;
}

/* First heading after content start should have less top margin */
.article > h1:first-child,
.article > h2:first-child,
.article > h3:first-child {
    margin-top: 0 !important;
}

/* Paragraph spacing */
p {
    margin-bottom: 1rem !important;
}

/* List spacing */
ul, ol {
    margin-bottom: 1rem !important;
}

ul li, ol li {
    margin-bottom: 0.25rem !important;
}

/* Nested list spacing */
ul ul, ol ol, ul ol, ol ul {
    margin-top: 0.25rem !important;
    margin-bottom: 0.5rem !important;
}

/* Code block spacing */
pre {
    margin-top: 1rem !important;
    margin-bottom: 1.5rem !important;
}

/* Blockquote spacing */
blockquote {
    margin-top: 1rem !important;
    margin-bottom: 1.5rem !important;
    padding-left: 1rem !important;
    border-left: 4px solid #ddd !important;
}

/* Table spacing */
table {
    margin-top: 1rem !important;
    margin-bottom: 1.5rem !important;
}

/* Horizontal rule spacing */
hr {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important;
}

/* Image spacing */
img {
    margin-top: 0.5rem !important;
    margin-bottom: 1rem !important;
}

/* Badge/shield spacing */
img[src*="shields.io"], img[src*="badge"] {
    margin: 0.25rem 0.25rem 0.25rem 0 !important;
    display: inline !important;
}