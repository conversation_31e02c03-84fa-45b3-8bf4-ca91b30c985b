{"metadata": [{"src": [{"files": ["src/Maui/DrawnUi/DrawnUi.Maui.csproj"], "src": "../", "exclude": ["**/bin/**", "**/obj/**"]}], "dest": "api", "disableGitFeatures": false, "disableDefaultFilter": false, "properties": {"TargetFramework": "net9.0"}}], "build": {"content": [{"files": ["api/**.yml", "api/index.md"]}, {"files": ["articles/**.md", "articles/**/toc.yml", "toc.yml", "*.md"]}], "resource": [{"files": ["images/**"]}], "overwrite": [{"files": ["apidoc/**.md"], "exclude": ["obj/**", "_site/**"]}], "dest": "_site", "globalMetadataFiles": [], "fileMetadataFiles": [], "template": ["default", "modern", "my-template"], "styles": {"website": ["styles/main.css"]}, "postProcessors": [], "markdownEngineName": "markdig", "noLangKeyword": false, "keepFileLink": false, "cleanupCacheHistory": false, "disableGitFeatures": false, "globalMetadata": {"_appTitle": "DrawnUI for .NET MAUI", "_enableSearch": true, "_appLogoPath": "images/logo.png", "_appFaviconPath": "images/favicon.ico", "_appFooter": "Created by <a href='https://github.com/taublast'><PERSON></a>", "_disableContribution": false, "_gitContribute": {"repo": "https://github.com/taublast/DrawnUi", "branch": "master"}, "meta": [{"name": "description", "content": "DrawnUI for .NET MAUI - Hardware-accelerated rendering engine built on SkiaSharp. Create pixel-perfect cross-platform apps for iOS, Android, Windows, MacCatalyst with advanced animations, gestures, and visual effects."}, {"name": "keywords", "content": ".NET MAUI, SkiaSharp, cross-platform UI, hardware acceleration, mobile development, iOS, Android, Windows, MacCatalyst, custom controls, animations, gestures, pixel-perfect, rendering engine"}, {"name": "author", "content": "<PERSON> (@taublast)"}, {"property": "og:title", "content": "DrawnUI for .NET MAUI - Hardware-Accelerated Rendering Engine"}, {"property": "og:description", "content": "Create stunning cross-platform apps with DrawnUI - a powerful rendering engine for .NET MAUI built on SkiaSharp. Hardware-accelerated performance with pixel-perfect controls."}, {"property": "og:type", "content": "website"}, {"property": "og:url", "content": "https://taublast.github.io/DrawnUi.Maui/"}, {"property": "og:image", "content": "https://taublast.github.io/DrawnUi.Maui/images/logo.png"}, {"name": "twitter:card", "content": "summary_large_image"}, {"name": "twitter:title", "content": "DrawnUI for .NET MAUI"}, {"name": "twitter:description", "content": "Hardware-accelerated rendering engine for .NET MAUI built on SkiaSharp"}]}}}