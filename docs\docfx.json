{"metadata": [{"src": [{"files": ["src/Maui/DrawnUi/DrawnUi.Maui.csproj"], "src": "../", "exclude": ["**/bin/**", "**/obj/**"]}], "dest": "api", "disableGitFeatures": false, "disableDefaultFilter": false, "properties": {"TargetFramework": "net9.0"}}], "build": {"content": [{"files": ["api/**.yml", "api/index.md"]}, {"files": ["articles/**.md", "articles/**/toc.yml", "toc.yml", "*.md"]}], "resource": [{"files": ["images/**"]}], "overwrite": [{"files": ["apidoc/**.md"], "exclude": ["obj/**", "_site/**"]}], "dest": "_site", "globalMetadataFiles": [], "fileMetadataFiles": [], "template": ["default", "modern", "templates/custom"], "postProcessors": [], "markdownEngineName": "markdig", "noLangKeyword": false, "keepFileLink": false, "cleanupCacheHistory": false, "disableGitFeatures": false, "globalMetadata": {"_appTitle": "DrawnUi Documentation", "_enableSearch": true, "_appLogoPath": "images/logo.png", "_appFaviconPath": "images/favicon.ico", "_disableContribution": false, "_gitContribute": {"repo": "https://github.com/taublast/DrawnUi", "branch": "master"}}}}